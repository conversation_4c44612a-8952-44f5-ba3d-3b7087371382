import { useState } from 'react'
import { useAuth } from '../context/AuthContext'

const Login = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [showPassword, setShowPassword] = useState(false)

  const { login } = useAuth()

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
    setError('')
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      await login(formData)
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50"></div>
      <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%239C92AC\' fill-opacity=\'0.03\'%3E%3Ccircle cx=\'30\' cy=\'30\' r=\'4\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40"></div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute bottom-20 right-20 w-40 h-40 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-10 w-24 h-24 bg-gradient-to-r from-indigo-400/20 to-blue-400/20 rounded-full blur-xl animate-pulse delay-500"></div>

      <div className="max-w-md w-full mx-4 relative z-10">
        {/* Logo and Header */}
        <div className="text-center mb-10 fade-in">
          <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 rounded-3xl mb-8 shadow-2xl shadow-blue-500/25 relative">
            <i className="ri-admin-line text-4xl text-white"></i>
            <div className="absolute -inset-1 bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl blur opacity-30 animate-pulse"></div>
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-3">
            لوحة التحكم
          </h1>
          <p className="text-gray-600 text-lg font-medium">عجائب الخبراء - إدارة المحتوى</p>
          <div className="w-20 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mx-auto mt-4"></div>
        </div>

        {/* Login Form */}
        <div className="glass-card p-10 scale-in backdrop-blur-xl bg-white/90 border border-white/20 shadow-2xl">
          <form onSubmit={handleSubmit} className="space-y-8">
            <div className="space-y-2">
              <label htmlFor="username" className="block text-sm font-semibold text-gray-800 mb-3">
                اسم المستخدم أو البريد الإلكتروني
              </label>
              <div className="relative group">
                <input
                  type="text"
                  id="username"
                  name="username"
                  value={formData.username}
                  onChange={handleChange}
                  className="form-input pl-14 h-14 text-lg"
                  placeholder="أدخل اسم المستخدم أو البريد الإلكتروني"
                  required
                />
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2 transition-colors duration-300 group-focus-within:text-blue-500">
                  <i className="ri-user-line text-xl text-gray-400"></i>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="password" className="block text-sm font-semibold text-gray-800 mb-3">
                كلمة المرور
              </label>
              <div className="relative group">
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  className="form-input pl-14 pr-14 h-14 text-lg"
                  placeholder="أدخل كلمة المرور"
                  required
                />
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2 transition-colors duration-300 group-focus-within:text-blue-500">
                  <i className="ri-lock-line text-xl text-gray-400"></i>
                </div>
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-blue-500 transition-colors duration-300 p-1"
                >
                  <i className={`${showPassword ? 'ri-eye-off-line' : 'ri-eye-line'} text-xl`}></i>
                </button>
              </div>
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-xl p-5 flex items-center space-x-3 rtl:space-x-reverse animate-pulse">
                <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                  <i className="ri-error-warning-line text-red-500 text-lg"></i>
                </div>
                <span className="text-red-700 font-medium">{error}</span>
              </div>
            )}

            <button
              type="submit"
              disabled={loading}
              className="w-full h-14 btn-primary flex items-center justify-center space-x-3 rtl:space-x-reverse disabled:opacity-50 disabled:cursor-not-allowed text-lg font-bold shadow-lg shadow-blue-500/25"
            >
              {loading ? (
                <>
                  <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>جاري تسجيل الدخول...</span>
                </>
              ) : (
                <>
                  <i className="ri-login-box-line text-xl"></i>
                  <span>تسجيل الدخول</span>
                </>
              )}
            </button>
          </form>

          {/* Default Credentials Info */}
          <div className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border border-blue-100">
            <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
              <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                <i className="ri-information-line text-white text-sm"></i>
              </div>
              <h3 className="text-lg font-bold text-blue-800">بيانات الدخول الافتراضية</h3>
            </div>
            <div className="bg-white/60 rounded-xl p-4 space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-blue-700 font-medium">اسم المستخدم:</span>
                <code className="bg-blue-100 text-blue-800 px-3 py-1 rounded-lg font-mono">admin</code>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-blue-700 font-medium">كلمة المرور:</span>
                <code className="bg-blue-100 text-blue-800 px-3 py-1 rounded-lg font-mono">admin123</code>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-10 fade-in">
          <p className="text-gray-500 font-medium">
            © 2024 عجائب الخبراء. جميع الحقوق محفوظة.
          </p>
        </div>
      </div>
    </div>
  )
}

export default Login
