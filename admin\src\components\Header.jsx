import { useAuth } from '../context/AuthContext'

const Header = ({ setSidebarOpen, activeSection }) => {
  const { user } = useAuth()

  const getSectionTitle = (section) => {
    const titles = {
      dashboard: 'الرئيسية',
      hero: 'إدارة الهيرو',
      'why-choose-us': 'لماذا تختارنا',
      kitchens: 'إدارة المطابخ',
      cabinets: 'إدارة الخزائن',
      footer: 'إدارة الفوتر',
      users: 'إدارة المستخدمين'
    }
    return titles[section] || 'لوحة التحكم'
  }

  const getSectionIcon = (section) => {
    const icons = {
      dashboard: 'ri-dashboard-line',
      hero: 'ri-image-line',
      'why-choose-us': 'ri-star-line',
      kitchens: 'ri-home-4-line',
      cabinets: 'ri-archive-line',
      footer: 'ri-links-line',
      users: 'ri-user-settings-line'
    }
    return icons[section] || 'ri-dashboard-line'
  }

  return (
    <header className="bg-white/80 backdrop-blur-xl shadow-lg border-b border-white/20 relative overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-50/30 via-purple-50/20 to-indigo-50/30"></div>

      <div className="flex items-center justify-between px-8 py-6 relative z-10">
        {/* Left Side - Section Info */}
        <div className="flex items-center space-x-6 rtl:space-x-reverse">
          <button
            onClick={() => setSidebarOpen(true)}
            className="lg:hidden p-3 rounded-2xl hover:bg-white/60 transition-all duration-300 hover:scale-110 backdrop-blur-sm shadow-md"
          >
            <i className="ri-menu-line text-xl text-gray-700"></i>
          </button>

          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="relative group">
              <div className="w-14 h-14 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-xl shadow-blue-500/25 transform transition-all duration-300 group-hover:scale-110 group-hover:rotate-3">
                <i className={`${getSectionIcon(activeSection)} text-white text-2xl`}></i>
              </div>
              <div className="absolute -inset-1 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl blur opacity-30 animate-pulse"></div>
            </div>
            <div className="space-y-1">
              <h1 className="text-2xl font-black bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">{getSectionTitle(activeSection)}</h1>
              <p className="text-sm text-gray-600 font-bold">إدارة محتوى الموقع</p>
              <div className="w-16 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
            </div>
          </div>
        </div>

        {/* Right Side - User Info & Actions */}
        <div className="flex items-center space-x-6 rtl:space-x-reverse">
          {/* Current Time */}
          <div className="hidden md:flex items-center space-x-3 rtl:space-x-reverse bg-white/60 backdrop-blur-sm rounded-2xl px-4 py-3 shadow-md border border-white/20">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <i className="ri-time-line text-white text-sm"></i>
            </div>
            <div className="text-right">
              <span className="text-sm font-bold text-gray-800 block">{new Date().toLocaleString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit'
              })}</span>
              <span className="text-xs text-gray-600 font-medium">{new Date().toLocaleString('ar-SA', {
                weekday: 'short',
                day: 'numeric',
                month: 'short'
              })}</span>
            </div>
          </div>

          {/* Notifications */}
          <button className="relative p-3 rounded-2xl hover:bg-white/60 transition-all duration-300 hover:scale-110 backdrop-blur-sm shadow-md border border-white/20 group">
            <i className="ri-notification-line text-xl text-gray-700 group-hover:text-blue-600 transition-colors duration-300"></i>
            <span className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full flex items-center justify-center font-bold shadow-lg animate-pulse">
              3
            </span>
          </button>

          {/* User Avatar */}
          <div className="flex items-center space-x-4 rtl:space-x-reverse bg-white/60 backdrop-blur-sm rounded-2xl px-4 py-3 shadow-md border border-white/20 hover:bg-white/80 transition-all duration-300 group">
            <div className="relative">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg transform transition-all duration-300 group-hover:scale-110">
                <span className="text-white font-black text-lg">
                  {user?.username?.charAt(0)?.toUpperCase() || 'A'}
                </span>
              </div>
              <div className="absolute -inset-1 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl blur opacity-30 animate-pulse"></div>
            </div>
            <div className="hidden md:block text-right space-y-1">
              <p className="text-sm font-bold text-gray-900">{user?.username || 'المدير'}</p>
              <p className="text-xs text-gray-600 font-medium">مدير النظام</p>
              <div className="flex items-center space-x-1 rtl:space-x-reverse">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-xs text-green-600 font-medium">متصل</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
