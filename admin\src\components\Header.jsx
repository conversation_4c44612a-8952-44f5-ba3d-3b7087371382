import { useAuth } from '../context/AuthContext'

const Header = ({ setSidebarOpen, activeSection }) => {
  const { user } = useAuth()

  const getSectionTitle = (section) => {
    const titles = {
      dashboard: 'الرئيسية',
      hero: 'إدارة الهيرو',
      'why-choose-us': 'لماذا تختارنا',
      kitchens: 'إدارة المطابخ',
      cabinets: 'إدارة الخزائن',
      footer: 'إدارة الفوتر',
      users: 'إدارة المستخدمين'
    }
    return titles[section] || 'لوحة التحكم'
  }

  const getSectionIcon = (section) => {
    const icons = {
      dashboard: 'ri-dashboard-line',
      hero: 'ri-image-line',
      'why-choose-us': 'ri-star-line',
      kitchens: 'ri-home-4-line',
      cabinets: 'ri-archive-line',
      footer: 'ri-links-line',
      users: 'ri-user-settings-line'
    }
    return icons[section] || 'ri-dashboard-line'
  }

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="flex items-center justify-between px-6 py-4">
        {/* Left Side - Section Info */}
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          <button
            onClick={() => setSidebarOpen(true)}
            className="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <i className="ri-menu-line text-xl text-gray-600"></i>
          </button>
          
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <i className={`${getSectionIcon(activeSection)} text-white text-lg`}></i>
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">{getSectionTitle(activeSection)}</h1>
              <p className="text-sm text-gray-500">إدارة محتوى الموقع</p>
            </div>
          </div>
        </div>

        {/* Right Side - User Info & Actions */}
        <div className="flex items-center space-x-4 rtl:space-x-reverse">
          {/* Current Time */}
          <div className="hidden md:flex items-center space-x-2 rtl:space-x-reverse text-sm text-gray-600">
            <i className="ri-time-line"></i>
            <span>{new Date().toLocaleString('ar-SA', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}</span>
          </div>

          {/* Notifications */}
          <button className="relative p-2 rounded-lg hover:bg-gray-100 transition-colors">
            <i className="ri-notification-line text-xl text-gray-600"></i>
            <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
              3
            </span>
          </button>

          {/* User Avatar */}
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-sm">
                {user?.username?.charAt(0)?.toUpperCase() || 'A'}
              </span>
            </div>
            <div className="hidden md:block text-right">
              <p className="text-sm font-medium text-gray-900">{user?.username || 'المدير'}</p>
              <p className="text-xs text-gray-500">مدير النظام</p>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
