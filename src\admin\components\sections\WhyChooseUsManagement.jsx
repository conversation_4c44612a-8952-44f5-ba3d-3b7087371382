import { useState } from 'react'
import { useData } from '../../context/DataContext'

const WhyChooseUsManagement = () => {
  const { whyChooseUsData, setWhyChooseUsData } = useData()
  const [isEditing, setIsEditing] = useState(false)
  const [editingFeature, setEditingFeature] = useState(null)
  const [formData, setFormData] = useState(whyChooseUsData)
  const [saving, setSaving] = useState(false)

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleFeatureChange = (index, field, value) => {
    const updatedFeatures = [...formData.features]
    updatedFeatures[index] = {
      ...updatedFeatures[index],
      [field]: value
    }
    setFormData({
      ...formData,
      features: updatedFeatures
    })
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      setWhyChooseUsData(formData)
      setIsEditing(false)
      setEditingFeature(null)
      alert('تم حفظ التغييرات بنجاح!')
    } catch (error) {
      alert('حدث خطأ أثناء الحفظ')
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    setFormData(whyChooseUsData)
    setIsEditing(false)
    setEditingFeature(null)
  }

  const addFeature = () => {
    const newFeature = {
      id: Date.now(),
      icon: 'ri-star-line',
      title: 'ميزة جديدة',
      description: 'وصف الميزة الجديدة',
      gradient: 'from-blue-500 to-cyan-500',
      bgGradient: 'from-blue-50 to-cyan-50',
      number: '1',
      subtitle: 'وصف قصير'
    }
    setFormData({
      ...formData,
      features: [...formData.features, newFeature]
    })
  }

  const deleteFeature = (index) => {
    if (window.confirm('هل أنت متأكد من حذف هذه الميزة؟')) {
      const updatedFeatures = formData.features.filter((_, i) => i !== index)
      setFormData({
        ...formData,
        features: updatedFeatures
      })
    }
  }

  const gradientOptions = [
    { value: 'from-blue-500 to-cyan-500', label: 'أزرق إلى سماوي', bg: 'from-blue-50 to-cyan-50' },
    { value: 'from-purple-500 to-pink-500', label: 'بنفسجي إلى وردي', bg: 'from-purple-50 to-pink-50' },
    { value: 'from-green-500 to-emerald-500', label: 'أخضر إلى زمردي', bg: 'from-green-50 to-emerald-50' },
    { value: 'from-orange-500 to-red-500', label: 'برتقالي إلى أحمر', bg: 'from-orange-50 to-red-50' },
    { value: 'from-yellow-500 to-orange-500', label: 'أصفر إلى برتقالي', bg: 'from-yellow-50 to-orange-50' }
  ]

  const iconOptions = [
    'ri-award-line', 'ri-palette-line', 'ri-tools-line', 'ri-star-line', 'ri-heart-line',
    'ri-shield-check-line', 'ri-time-line', 'ri-user-line', 'ri-settings-line', 'ri-lightbulb-line'
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة قسم "لماذا تختارنا"</h1>
          <p className="text-gray-600 mt-1">تحرير عنوان القسم والمميزات</p>
        </div>
        <div className="flex space-x-3 rtl:space-x-reverse">
          {isEditing ? (
            <>
              <button onClick={handleCancel} className="btn-secondary" disabled={saving}>
                إلغاء
              </button>
              <button onClick={handleSave} className="btn-primary" disabled={saving}>
                {saving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
              </button>
            </>
          ) : (
            <button onClick={() => setIsEditing(true)} className="btn-primary">
              <i className="ri-edit-line ml-2"></i>
              تعديل
            </button>
          )}
        </div>
      </div>

      {/* Section Header Edit */}
      {isEditing && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-bold text-gray-900 mb-6">تحرير عنوان القسم</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">العنوان الرئيسي</label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleChange}
                className="form-input"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">العنوان الفرعي</label>
              <textarea
                name="subtitle"
                value={formData.subtitle}
                onChange={handleChange}
                rows={2}
                className="form-textarea"
              />
            </div>
          </div>
        </div>
      )}

      {/* Features Management */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-bold text-gray-900">إدارة المميزات</h2>
          {isEditing && (
            <button onClick={addFeature} className="btn-success">
              <i className="ri-add-line ml-2"></i>
              إضافة ميزة جديدة
            </button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {formData.features.map((feature, index) => (
            <div key={feature.id} className="border border-gray-200 rounded-xl p-6 relative">
              {isEditing && (
                <div className="absolute top-2 left-2 flex space-x-2 rtl:space-x-reverse">
                  <button
                    onClick={() => setEditingFeature(editingFeature === index ? null : index)}
                    className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center hover:bg-blue-200"
                  >
                    <i className="ri-edit-line text-sm"></i>
                  </button>
                  <button
                    onClick={() => deleteFeature(index)}
                    className="w-8 h-8 bg-red-100 text-red-600 rounded-full flex items-center justify-center hover:bg-red-200"
                  >
                    <i className="ri-delete-bin-line text-sm"></i>
                  </button>
                </div>
              )}

              {editingFeature === index ? (
                <div className="space-y-4 mt-8">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">الأيقونة</label>
                    <select
                      value={feature.icon}
                      onChange={(e) => handleFeatureChange(index, 'icon', e.target.value)}
                      className="form-input"
                    >
                      {iconOptions.map(icon => (
                        <option key={icon} value={icon}>{icon}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                    <input
                      type="text"
                      value={feature.title}
                      onChange={(e) => handleFeatureChange(index, 'title', e.target.value)}
                      className="form-input"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                    <textarea
                      value={feature.description}
                      onChange={(e) => handleFeatureChange(index, 'description', e.target.value)}
                      rows={3}
                      className="form-textarea"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">الرقم</label>
                    <input
                      type="text"
                      value={feature.number}
                      onChange={(e) => handleFeatureChange(index, 'number', e.target.value)}
                      className="form-input"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">الوصف القصير</label>
                    <input
                      type="text"
                      value={feature.subtitle}
                      onChange={(e) => handleFeatureChange(index, 'subtitle', e.target.value)}
                      className="form-input"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">اللون</label>
                    <select
                      value={feature.gradient}
                      onChange={(e) => {
                        const selectedGradient = gradientOptions.find(g => g.value === e.target.value)
                        handleFeatureChange(index, 'gradient', e.target.value)
                        handleFeatureChange(index, 'bgGradient', selectedGradient.bg)
                      }}
                      className="form-input"
                    >
                      {gradientOptions.map(gradient => (
                        <option key={gradient.value} value={gradient.value}>{gradient.label}</option>
                      ))}
                    </select>
                  </div>
                </div>
              ) : (
                <div className="text-center">
                  <div className={`w-16 h-16 mx-auto bg-gradient-to-r ${feature.gradient} rounded-2xl flex items-center justify-center mb-4 relative`}>
                    <i className={`${feature.icon} text-2xl text-white`}></i>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center border-2 border-gray-50">
                      <span className={`text-xs font-bold bg-gradient-to-r ${feature.gradient} bg-clip-text text-transparent`}>
                        {feature.number}
                      </span>
                    </div>
                  </div>
                  <h3 className="font-bold text-gray-900 mb-2">{feature.title}</h3>
                  <p className="text-sm text-gray-600 mb-3">{feature.description}</p>
                  <p className="text-xs text-gray-500">{feature.subtitle}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Preview */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-bold text-gray-900 mb-6">معاينة القسم</h2>
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">{formData.title}</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">{formData.subtitle}</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {formData.features.map((feature, index) => (
            <div key={feature.id} className="text-center p-6 bg-gray-50 rounded-xl">
              <div className={`w-20 h-20 mx-auto bg-gradient-to-r ${feature.gradient} rounded-2xl flex items-center justify-center mb-6 relative`}>
                <i className={`${feature.icon} text-3xl text-white`}></i>
                <div className="absolute -top-2 -right-2 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center border-4 border-gray-50">
                  <span className={`text-sm font-bold bg-gradient-to-r ${feature.gradient} bg-clip-text text-transparent`}>
                    {feature.number}
                  </span>
                </div>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
              <p className="text-gray-600 mb-4">{feature.description}</p>
              <p className="text-sm text-gray-500">{feature.subtitle}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default WhyChooseUsManagement
