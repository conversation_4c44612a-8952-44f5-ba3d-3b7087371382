import { useState } from 'react'
import { useAuth } from '../../context/AuthContext'

const UserManagement = () => {
  const { user, updateUser } = useAuth()
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    username: user?.username || '',
    email: user?.email || '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  const [saving, setSaving] = useState(false)
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  })

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const togglePasswordVisibility = (field) => {
    setShowPasswords({
      ...showPasswords,
      [field]: !showPasswords[field]
    })
  }

  const handleSave = async () => {
    // Validate form
    if (!formData.username.trim() || !formData.email.trim()) {
      alert('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    if (formData.newPassword && formData.newPassword !== formData.confirmPassword) {
      alert('كلمة المرور الجديدة وتأكيدها غير متطابقين')
      return
    }

    if (formData.newPassword && formData.newPassword.length < 6) {
      alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
      return
    }

    setSaving(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const updateData = {
        username: formData.username,
        email: formData.email
      }

      if (formData.newPassword) {
        updateData.password = formData.newPassword
      }

      updateUser(updateData)
      setIsEditing(false)
      setFormData({
        ...formData,
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      })
      alert('تم تحديث البيانات بنجاح!')
    } catch (error) {
      alert('حدث خطأ أثناء التحديث')
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    setFormData({
      username: user?.username || '',
      email: user?.email || '',
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    })
    setIsEditing(false)
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة المستخدمين</h1>
          <p className="text-gray-600 mt-1">تعديل بيانات المستخدم وكلمة المرور</p>
        </div>
        <div className="flex space-x-3 rtl:space-x-reverse">
          {isEditing ? (
            <>
              <button onClick={handleCancel} className="btn-secondary" disabled={saving}>
                إلغاء
              </button>
              <button onClick={handleSave} className="btn-primary" disabled={saving}>
                {saving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
              </button>
            </>
          ) : (
            <button onClick={() => setIsEditing(true)} className="btn-primary">
              <i className="ri-edit-line ml-2"></i>
              تعديل البيانات
            </button>
          )}
        </div>
      </div>

      {/* User Profile Card */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-6 rtl:space-x-reverse mb-8">
          <div className="w-24 h-24 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
            <span className="text-white font-bold text-3xl">
              {user?.username?.charAt(0)?.toUpperCase() || 'A'}
            </span>
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{user?.username || 'المدير'}</h2>
            <p className="text-gray-600">{user?.email || '<EMAIL>'}</p>
            <div className="flex items-center space-x-2 rtl:space-x-reverse mt-2">
              <span className="badge badge-success">مدير النظام</span>
              <span className="badge badge-info">نشط</span>
            </div>
          </div>
        </div>

        {/* Basic Information */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 className="text-lg font-bold text-gray-900 mb-4">المعلومات الأساسية</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اسم المستخدم *
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    name="username"
                    value={formData.username}
                    onChange={handleChange}
                    className="form-input"
                    placeholder="أدخل اسم المستخدم"
                  />
                ) : (
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <span className="text-gray-900">{user?.username || 'غير محدد'}</span>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  البريد الإلكتروني *
                </label>
                {isEditing ? (
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    className="form-input"
                    placeholder="أدخل البريد الإلكتروني"
                  />
                ) : (
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <span className="text-gray-900">{user?.email || 'غير محدد'}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Password Change */}
          {isEditing && (
            <div>
              <h3 className="text-lg font-bold text-gray-900 mb-4">تغيير كلمة المرور</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    كلمة المرور الحالية
                  </label>
                  <div className="relative">
                    <input
                      type={showPasswords.current ? 'text' : 'password'}
                      name="currentPassword"
                      value={formData.currentPassword}
                      onChange={handleChange}
                      className="form-input pr-12"
                      placeholder="أدخل كلمة المرور الحالية"
                    />
                    <button
                      type="button"
                      onClick={() => togglePasswordVisibility('current')}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      <i className={showPasswords.current ? 'ri-eye-off-line' : 'ri-eye-line'}></i>
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    كلمة المرور الجديدة
                  </label>
                  <div className="relative">
                    <input
                      type={showPasswords.new ? 'text' : 'password'}
                      name="newPassword"
                      value={formData.newPassword}
                      onChange={handleChange}
                      className="form-input pr-12"
                      placeholder="أدخل كلمة المرور الجديدة"
                    />
                    <button
                      type="button"
                      onClick={() => togglePasswordVisibility('new')}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      <i className={showPasswords.new ? 'ri-eye-off-line' : 'ri-eye-line'}></i>
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    تأكيد كلمة المرور الجديدة
                  </label>
                  <div className="relative">
                    <input
                      type={showPasswords.confirm ? 'text' : 'password'}
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleChange}
                      className="form-input pr-12"
                      placeholder="أعد إدخال كلمة المرور الجديدة"
                    />
                    <button
                      type="button"
                      onClick={() => togglePasswordVisibility('confirm')}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      <i className={showPasswords.confirm ? 'ri-eye-off-line' : 'ri-eye-line'}></i>
                    </button>
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-blue-800 mb-2">متطلبات كلمة المرور:</h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li className="flex items-center space-x-2 rtl:space-x-reverse">
                      <i className={`ri-check-line ${formData.newPassword.length >= 6 ? 'text-green-600' : 'text-gray-400'}`}></i>
                      <span>6 أحرف على الأقل</span>
                    </li>
                    <li className="flex items-center space-x-2 rtl:space-x-reverse">
                      <i className={`ri-check-line ${formData.newPassword === formData.confirmPassword && formData.newPassword ? 'text-green-600' : 'text-gray-400'}`}></i>
                      <span>تطابق كلمة المرور وتأكيدها</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Account Statistics */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-bold text-gray-900 mb-6">إحصائيات الحساب</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-6 bg-blue-50 rounded-xl">
            <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <i className="ri-login-box-line text-2xl text-white"></i>
            </div>
            <h4 className="text-2xl font-bold text-blue-600">1</h4>
            <p className="text-blue-800 font-medium">جلسة نشطة</p>
          </div>

          <div className="text-center p-6 bg-green-50 rounded-xl">
            <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <i className="ri-time-line text-2xl text-white"></i>
            </div>
            <h4 className="text-2xl font-bold text-green-600">اليوم</h4>
            <p className="text-green-800 font-medium">آخر تسجيل دخول</p>
          </div>

          <div className="text-center p-6 bg-purple-50 rounded-xl">
            <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <i className="ri-shield-check-line text-2xl text-white"></i>
            </div>
            <h4 className="text-2xl font-bold text-purple-600">آمن</h4>
            <p className="text-purple-800 font-medium">حالة الأمان</p>
          </div>
        </div>
      </div>

      {/* Security Settings */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-bold text-gray-900 mb-6">إعدادات الأمان</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <i className="ri-shield-check-line text-green-600"></i>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">المصادقة الثنائية</h4>
                <p className="text-sm text-gray-600">حماية إضافية لحسابك</p>
              </div>
            </div>
            <span className="badge badge-success">مفعل</span>
          </div>

          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <i className="ri-notification-line text-blue-600"></i>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">تنبيهات الأمان</h4>
                <p className="text-sm text-gray-600">إشعارات عند تسجيل الدخول</p>
              </div>
            </div>
            <span className="badge badge-success">مفعل</span>
          </div>

          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <i className="ri-history-line text-orange-600"></i>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">سجل النشاط</h4>
                <p className="text-sm text-gray-600">تتبع جميع العمليات</p>
              </div>
            </div>
            <span className="badge badge-success">مفعل</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default UserManagement
