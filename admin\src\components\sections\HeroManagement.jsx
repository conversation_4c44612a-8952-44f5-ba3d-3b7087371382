import { useState } from 'react'
import { useData } from '../../context/DataContext'

const HeroManagement = () => {
  const { heroData, setHeroData } = useData()
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState(heroData)
  const [saving, setSaving] = useState(false)

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      setHeroData(formData)
      setIsEditing(false)
      alert('تم حفظ التغييرات بنجاح!')
    } catch (error) {
      alert('حدث خطأ أثناء الحفظ')
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    setFormData(heroData)
    setIsEditing(false)
  }

  const handleImageUpload = (e) => {
    const file = e.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        setFormData({
          ...formData,
          backgroundImage: e.target.result
        })
      }
      reader.readAsDataURL(file)
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة قسم الهيرو</h1>
          <p className="text-gray-600 mt-1">تحرير النصوص والصور في القسم الرئيسي للموقع</p>
        </div>
        <div className="flex space-x-3 rtl:space-x-reverse">
          {isEditing ? (
            <>
              <button
                onClick={handleCancel}
                className="btn-secondary"
                disabled={saving}
              >
                إلغاء
              </button>
              <button
                onClick={handleSave}
                className="btn-primary"
                disabled={saving}
              >
                {saving ? (
                  <>
                    <div className="spinner w-4 h-4 ml-2"></div>
                    جاري الحفظ...
                  </>
                ) : (
                  <>
                    <i className="ri-save-line ml-2"></i>
                    حفظ التغييرات
                  </>
                )}
              </button>
            </>
          ) : (
            <button
              onClick={() => setIsEditing(true)}
              className="btn-primary"
            >
              <i className="ri-edit-line ml-2"></i>
              تعديل
            </button>
          )}
        </div>
      </div>

      {/* Preview */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-bold text-gray-900 flex items-center space-x-2 rtl:space-x-reverse">
            <i className="ri-eye-line text-blue-600"></i>
            <span>معاينة القسم</span>
          </h2>
        </div>
        
        <div 
          className="relative h-96 bg-cover bg-center flex items-center"
          style={{ 
            backgroundImage: `linear-gradient(to left, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.7)), url(${formData.backgroundImage})` 
          }}
        >
          <div className="container mx-auto px-6 text-white">
            <div className="max-w-4xl">
              <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                {formData.title}
              </h1>
              <p className="text-xl text-gray-100 mb-8 leading-relaxed">
                {formData.subtitle}
              </p>
              <div className="flex flex-wrap gap-4">
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-medium transition-all duration-300">
                  {formData.primaryButtonText}
                </button>
                <button className="bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 text-white px-8 py-4 rounded-lg text-lg font-medium transition-all duration-300">
                  {formData.secondaryButtonText}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Edit Form */}
      {isEditing && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-bold text-gray-900 mb-6 flex items-center space-x-2 rtl:space-x-reverse">
            <i className="ri-edit-line text-blue-600"></i>
            <span>تحرير المحتوى</span>
          </h2>

          <div className="space-y-6">
            {/* Title */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                العنوان الرئيسي
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleChange}
                className="form-input"
                placeholder="أدخل العنوان الرئيسي"
              />
            </div>

            {/* Subtitle */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                العنوان الفرعي
              </label>
              <textarea
                name="subtitle"
                value={formData.subtitle}
                onChange={handleChange}
                rows={3}
                className="form-textarea"
                placeholder="أدخل العنوان الفرعي"
              />
            </div>

            {/* Primary Button Text */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نص الزر الأساسي
              </label>
              <input
                type="text"
                name="primaryButtonText"
                value={formData.primaryButtonText}
                onChange={handleChange}
                className="form-input"
                placeholder="أدخل نص الزر الأساسي"
              />
            </div>

            {/* Secondary Button Text */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نص الزر الثانوي
              </label>
              <input
                type="text"
                name="secondaryButtonText"
                value={formData.secondaryButtonText}
                onChange={handleChange}
                className="form-input"
                placeholder="أدخل نص الزر الثانوي"
              />
            </div>

            {/* Background Image */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                صورة الخلفية
              </label>
              <div className="space-y-4">
                <input
                  type="url"
                  name="backgroundImage"
                  value={formData.backgroundImage}
                  onChange={handleChange}
                  className="form-input"
                  placeholder="أدخل رابط الصورة"
                />
                <div className="text-center">
                  <span className="text-gray-500">أو</span>
                </div>
                <div className="image-upload-area">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    id="hero-image-upload"
                  />
                  <label htmlFor="hero-image-upload" className="cursor-pointer">
                    <i className="ri-upload-cloud-line text-4xl text-gray-400 mb-4"></i>
                    <p className="text-gray-600 font-medium">اضغط لرفع صورة جديدة</p>
                    <p className="text-sm text-gray-500 mt-2">PNG, JPG, GIF حتى 10MB</p>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Current Data Display */}
      {!isEditing && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-bold text-gray-900 mb-6 flex items-center space-x-2 rtl:space-x-reverse">
            <i className="ri-information-line text-blue-600"></i>
            <span>البيانات الحالية</span>
          </h2>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">العنوان الرئيسي</h3>
                <p className="text-gray-900 bg-gray-50 p-3 rounded-lg">{heroData.title}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">العنوان الفرعي</h3>
                <p className="text-gray-900 bg-gray-50 p-3 rounded-lg">{heroData.subtitle}</p>
              </div>
            </div>
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">نص الزر الأساسي</h3>
                <p className="text-gray-900 bg-gray-50 p-3 rounded-lg">{heroData.primaryButtonText}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">نص الزر الثانوي</h3>
                <p className="text-gray-900 bg-gray-50 p-3 rounded-lg">{heroData.secondaryButtonText}</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default HeroManagement
