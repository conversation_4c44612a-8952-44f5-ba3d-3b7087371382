import { createContext, useContext, useState, useEffect } from 'react'

const AuthContext = createContext()

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState(null)

  // Default admin credentials
  const defaultCredentials = {
    username: 'admin',
    email: '<EMAIL>',
    password: 'admin123'
  }

  useEffect(() => {
    // Check if user is already logged in
    const token = localStorage.getItem('admin_token')
    const userData = localStorage.getItem('admin_user')
    
    if (token && userData) {
      setIsAuthenticated(true)
      setUser(JSON.parse(userData))
    }
    
    setLoading(false)
  }, [])

  const login = (credentials) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // Get stored user data or use default
        const storedUser = JSON.parse(localStorage.getItem('admin_user_data') || JSON.stringify(defaultCredentials))
        
        if (
          (credentials.username === storedUser.username || credentials.email === storedUser.email) &&
          credentials.password === storedUser.password
        ) {
          const token = 'admin_token_' + Date.now()
          const userData = {
            username: storedUser.username,
            email: storedUser.email
          }
          
          localStorage.setItem('admin_token', token)
          localStorage.setItem('admin_user', JSON.stringify(userData))
          
          setIsAuthenticated(true)
          setUser(userData)
          resolve(userData)
        } else {
          reject(new Error('بيانات الدخول غير صحيحة'))
        }
      }, 1000)
    })
  }

  const logout = () => {
    localStorage.removeItem('admin_token')
    localStorage.removeItem('admin_user')
    setIsAuthenticated(false)
    setUser(null)
  }

  const updateUser = (userData) => {
    // Update stored user data
    const currentData = JSON.parse(localStorage.getItem('admin_user_data') || JSON.stringify(defaultCredentials))
    const updatedData = { ...currentData, ...userData }
    
    localStorage.setItem('admin_user_data', JSON.stringify(updatedData))
    
    // Update current user session if username/email changed
    if (userData.username || userData.email) {
      const updatedUser = {
        username: userData.username || user.username,
        email: userData.email || user.email
      }
      localStorage.setItem('admin_user', JSON.stringify(updatedUser))
      setUser(updatedUser)
    }
  }

  const value = {
    isAuthenticated,
    user,
    loading,
    login,
    logout,
    updateUser
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
