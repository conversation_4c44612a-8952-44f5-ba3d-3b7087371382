const Testimonials = () => {
  const testimonials = [
    {
      id: 1,
      name: "أحمد الراشدي",
      location: "الرياض",
      rating: 5,
      text: "تعاملت مع شركة عجائب الخبراء لتصميم وتنفيذ مطبخ منزلي الجديد، وكانت التجربة رائعة من البداية للنهاية. الجودة ممتازة والتصميم فاق توقعاتي.",
      initial: "أ"
    },
    {
      id: 2,
      name: "سارة العتيبي",
      location: "جدة",
      rating: 5,
      text: "خزائن الملابس التي صممتها وركبتها شركة عجائب الخبراء في غرف النوم كانت تحفة فنية. التنظيم الداخلي مدروس بعناية والخامات ممتازة. أنصح بهم بشدة.",
      initial: "س"
    },
    {
      id: 3,
      name: "محمد القحطاني",
      location: "الدمام",
      rating: 4.5,
      text: "الاحترافية في التعامل والدقة في التنفيذ هي ما يميز شركة عجائب الخبراء. المطبخ الذي صمموه لي يجمع بين الجمال والعملية، وبعد سنتين ما زال بحالة ممتازة.",
      initial: "م"
    }
  ];

  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<i key={i} className="ri-star-fill text-yellow-400"></i>);
    }

    if (hasHalfStar) {
      stars.push(<i key="half" className="ri-star-half-fill text-yellow-400"></i>);
    }

    const remainingStars = 5 - Math.ceil(rating);
    for (let i = 0; i < remainingStars; i++) {
      stars.push(<i key={`empty-${i}`} className="ri-star-line text-yellow-400"></i>);
    }

    return stars;
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-6">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            ماذا يقول عملاؤنا
          </h2>
          <p className="text-gray-600 max-w-3xl mx-auto">
            آراء عملائنا الكرام في خدماتنا ومنتجاتنا
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {testimonials.map((testimonial) => (
            <div key={testimonial.id} className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
              <div className="flex items-center mb-4">
                <div className="text-yellow-400 flex">
                  {renderStars(testimonial.rating)}
                </div>
              </div>
              <p className="text-gray-600 mb-4 leading-relaxed">
                "{testimonial.text}"
              </p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-primary font-bold">
                  {testimonial.initial}
                </div>
                <div className="mr-3">
                  <h4 className="font-medium text-gray-900">{testimonial.name}</h4>
                  <p className="text-sm text-gray-500">{testimonial.location}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
