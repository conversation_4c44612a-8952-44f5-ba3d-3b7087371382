import { useState } from 'react'
import { useData } from '../../context/DataContext'

const FooterManagement = () => {
  const { footerData, setFooterData } = useData()
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState(footerData)
  const [saving, setSaving] = useState(false)

  const handleChange = (field, value) => {
    setFormData({
      ...formData,
      [field]: value
    })
  }

  const handleSocialMediaChange = (index, field, value) => {
    const updatedSocialMedia = [...formData.socialMedia]
    updatedSocialMedia[index] = {
      ...updatedSocialMedia[index],
      [field]: value
    }
    setFormData({
      ...formData,
      socialMedia: updatedSocialMedia
    })
  }

  const handleQuickLinksChange = (index, field, value) => {
    const updatedQuickLinks = [...formData.quickLinks]
    updatedQuickLinks[index] = {
      ...updatedQuickLinks[index],
      [field]: value
    }
    setFormData({
      ...formData,
      quickLinks: updatedQuickLinks
    })
  }

  const handleContactInfoChange = (index, field, value) => {
    const updatedContactInfo = [...formData.contactInfo]
    updatedContactInfo[index] = {
      ...updatedContactInfo[index],
      [field]: value
    }
    setFormData({
      ...formData,
      contactInfo: updatedContactInfo
    })
  }

  const addSocialMedia = () => {
    const newSocialMedia = {
      platform: 'new-platform',
      url: 'https://',
      icon: 'ri-link-line'
    }
    setFormData({
      ...formData,
      socialMedia: [...formData.socialMedia, newSocialMedia]
    })
  }

  const removeSocialMedia = (index) => {
    const updatedSocialMedia = formData.socialMedia.filter((_, i) => i !== index)
    setFormData({
      ...formData,
      socialMedia: updatedSocialMedia
    })
  }

  const addQuickLink = () => {
    const newQuickLink = {
      href: '#',
      text: 'رابط جديد'
    }
    setFormData({
      ...formData,
      quickLinks: [...formData.quickLinks, newQuickLink]
    })
  }

  const removeQuickLink = (index) => {
    const updatedQuickLinks = formData.quickLinks.filter((_, i) => i !== index)
    setFormData({
      ...formData,
      quickLinks: updatedQuickLinks
    })
  }

  const addContactInfo = () => {
    const newContactInfo = {
      icon: 'ri-information-line',
      text: 'معلومة جديدة'
    }
    setFormData({
      ...formData,
      contactInfo: [...formData.contactInfo, newContactInfo]
    })
  }

  const removeContactInfo = (index) => {
    const updatedContactInfo = formData.contactInfo.filter((_, i) => i !== index)
    setFormData({
      ...formData,
      contactInfo: updatedContactInfo
    })
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      setFooterData(formData)
      setIsEditing(false)
      alert('تم حفظ التغييرات بنجاح!')
    } catch (error) {
      alert('حدث خطأ أثناء الحفظ')
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    setFormData(footerData)
    setIsEditing(false)
  }

  const socialMediaIcons = [
    { value: 'ri-twitter-line', label: 'تويتر' },
    { value: 'ri-snapchat-line', label: 'سناب شات' },
    { value: 'ri-instagram-line', label: 'إنستغرام' },
    { value: 'ri-whatsapp-line', label: 'واتساب' },
    { value: 'ri-tiktok-line', label: 'تيك توك' },
    { value: 'ri-facebook-line', label: 'فيسبوك' },
    { value: 'ri-youtube-line', label: 'يوتيوب' },
    { value: 'ri-linkedin-line', label: 'لينكد إن' }
  ]

  const contactIcons = [
    { value: 'ri-map-pin-line', label: 'الموقع' },
    { value: 'ri-phone-line', label: 'الهاتف' },
    { value: 'ri-mail-line', label: 'البريد الإلكتروني' },
    { value: 'ri-time-line', label: 'أوقات العمل' },
    { value: 'ri-information-line', label: 'معلومات عامة' }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة الفوتر</h1>
          <p className="text-gray-600 mt-1">تحرير معلومات التواصل والروابط في أسفل الموقع</p>
        </div>
        <div className="flex space-x-3 rtl:space-x-reverse">
          {isEditing ? (
            <>
              <button onClick={handleCancel} className="btn-secondary" disabled={saving}>
                إلغاء
              </button>
              <button onClick={handleSave} className="btn-primary" disabled={saving}>
                {saving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
              </button>
            </>
          ) : (
            <button onClick={() => setIsEditing(true)} className="btn-primary">
              <i className="ri-edit-line ml-2"></i>
              تعديل
            </button>
          )}
        </div>
      </div>

      {/* Social Media Management */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-bold text-gray-900 flex items-center space-x-2 rtl:space-x-reverse">
            <i className="ri-share-line text-blue-600"></i>
            <span>مواقع التواصل الاجتماعي</span>
          </h2>
          {isEditing && (
            <button onClick={addSocialMedia} className="btn-success">
              <i className="ri-add-line ml-2"></i>
              إضافة موقع
            </button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {formData.socialMedia.map((social, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4">
              {isEditing ? (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium text-gray-700">المنصة</label>
                    <button
                      onClick={() => removeSocialMedia(index)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <i className="ri-delete-bin-line"></i>
                    </button>
                  </div>
                  <input
                    type="text"
                    value={social.platform}
                    onChange={(e) => handleSocialMediaChange(index, 'platform', e.target.value)}
                    className="form-input"
                    placeholder="اسم المنصة"
                  />
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">الأيقونة</label>
                    <select
                      value={social.icon}
                      onChange={(e) => handleSocialMediaChange(index, 'icon', e.target.value)}
                      className="form-input"
                    >
                      {socialMediaIcons.map(icon => (
                        <option key={icon.value} value={icon.value}>{icon.label}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">الرابط</label>
                    <input
                      type="url"
                      value={social.url}
                      onChange={(e) => handleSocialMediaChange(index, 'url', e.target.value)}
                      className="form-input"
                      placeholder="https://..."
                    />
                  </div>
                </div>
              ) : (
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i className={`${social.icon} text-blue-600`}></i>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">{social.platform}</h3>
                    <p className="text-sm text-gray-600">{social.url}</p>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Quick Links Management */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-bold text-gray-900 flex items-center space-x-2 rtl:space-x-reverse">
            <i className="ri-links-line text-blue-600"></i>
            <span>الروابط السريعة</span>
          </h2>
          {isEditing && (
            <button onClick={addQuickLink} className="btn-success">
              <i className="ri-add-line ml-2"></i>
              إضافة رابط
            </button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {formData.quickLinks.map((link, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4">
              {isEditing ? (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium text-gray-700">الرابط</label>
                    <button
                      onClick={() => removeQuickLink(index)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <i className="ri-delete-bin-line"></i>
                    </button>
                  </div>
                  <input
                    type="text"
                    value={link.text}
                    onChange={(e) => handleQuickLinksChange(index, 'text', e.target.value)}
                    className="form-input"
                    placeholder="نص الرابط"
                  />
                  <input
                    type="text"
                    value={link.href}
                    onChange={(e) => handleQuickLinksChange(index, 'href', e.target.value)}
                    className="form-input"
                    placeholder="عنوان الرابط"
                  />
                </div>
              ) : (
                <div>
                  <h3 className="font-medium text-gray-900">{link.text}</h3>
                  <p className="text-sm text-gray-600">{link.href}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Contact Info Management */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-bold text-gray-900 flex items-center space-x-2 rtl:space-x-reverse">
            <i className="ri-contacts-line text-blue-600"></i>
            <span>معلومات التواصل</span>
          </h2>
          {isEditing && (
            <button onClick={addContactInfo} className="btn-success">
              <i className="ri-add-line ml-2"></i>
              إضافة معلومة
            </button>
          )}
        </div>

        <div className="space-y-4">
          {formData.contactInfo.map((contact, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4">
              {isEditing ? (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium text-gray-700">معلومة التواصل</label>
                    <button
                      onClick={() => removeContactInfo(index)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <i className="ri-delete-bin-line"></i>
                    </button>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">الأيقونة</label>
                    <select
                      value={contact.icon}
                      onChange={(e) => handleContactInfoChange(index, 'icon', e.target.value)}
                      className="form-input"
                    >
                      {contactIcons.map(icon => (
                        <option key={icon.value} value={icon.value}>{icon.label}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">النص</label>
                    <textarea
                      value={contact.text}
                      onChange={(e) => handleContactInfoChange(index, 'text', e.target.value)}
                      className="form-textarea"
                      rows={2}
                      placeholder="معلومات التواصل"
                    />
                  </div>
                </div>
              ) : (
                <div className="flex items-start space-x-3 rtl:space-x-reverse">
                  <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mt-1">
                    <i className={`${contact.icon} text-green-600`}></i>
                  </div>
                  <div>
                    <p className="text-gray-900">{contact.text}</p>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Copyright Management */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-bold text-gray-900 mb-6 flex items-center space-x-2 rtl:space-x-reverse">
          <i className="ri-copyright-line text-blue-600"></i>
          <span>حقوق الطبع والنشر</span>
        </h2>

        {isEditing ? (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">نص حقوق الطبع والنشر</label>
            <input
              type="text"
              value={formData.copyright}
              onChange={(e) => handleChange('copyright', e.target.value)}
              className="form-input"
              placeholder="© 2024 عجائب الخبراء. جميع الحقوق محفوظة."
            />
          </div>
        ) : (
          <div className="bg-gray-50 rounded-lg p-4">
            <p className="text-gray-900 text-center">{formData.copyright}</p>
          </div>
        )}
      </div>

      {/* Preview */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-bold text-gray-900 mb-6 flex items-center space-x-2 rtl:space-x-reverse">
          <i className="ri-eye-line text-blue-600"></i>
          <span>معاينة الفوتر</span>
        </h2>

        <div className="bg-gray-900 text-white p-8 rounded-lg">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Social Media */}
            <div>
              <h3 className="text-lg font-bold mb-4">تابعنا</h3>
              <div className="flex space-x-4 rtl:space-x-reverse">
                {formData.socialMedia.map((social, index) => (
                  <a key={index} href={social.url} className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors">
                    <i className={social.icon}></i>
                  </a>
                ))}
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="text-lg font-bold mb-4">روابط سريعة</h3>
              <ul className="space-y-2">
                {formData.quickLinks.map((link, index) => (
                  <li key={index}>
                    <a href={link.href} className="text-gray-300 hover:text-white transition-colors">
                      {link.text}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <h3 className="text-lg font-bold mb-4">تواصل معنا</h3>
              <div className="space-y-3">
                {formData.contactInfo.map((contact, index) => (
                  <div key={index} className="flex items-start space-x-3 rtl:space-x-reverse">
                    <i className={`${contact.icon} text-blue-400 mt-1`}></i>
                    <span className="text-gray-300 text-sm">{contact.text}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="border-t border-gray-700 mt-8 pt-8 text-center">
            <p className="text-gray-400">{formData.copyright}</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FooterManagement
