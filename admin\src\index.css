@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: 'Inter', 'Cairo', sans-serif;
  line-height: 1.6;
  font-weight: 400;

  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;

  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  color-scheme: light;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  font-family: 'Inter', 'Cairo', sans-serif;
  background: var(--gray-50);
  color: var(--gray-900);
  overflow-x: hidden;
}

#root {
  width: 100%;
  min-height: 100vh;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  border-radius: 4px;
  border: 2px solid var(--gray-100);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
}

::-webkit-scrollbar-corner {
  background: var(--gray-100);
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(24px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.scale-in {
  animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.bounce-in {
  animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes bounceIn {
  from {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Form Styles */
.form-input {
  @apply w-full px-4 py-3.5 border border-gray-200 rounded-xl bg-white text-gray-900 placeholder-gray-400
         focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 focus:outline-none
         transition-all duration-300 hover:border-gray-300 shadow-sm;
}

.form-input:focus {
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1), 0 1px 3px 0 rgb(0 0 0 / 0.1);
}

.form-textarea {
  @apply w-full px-4 py-3.5 border border-gray-200 rounded-xl bg-white text-gray-900 placeholder-gray-400
         focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 focus:outline-none
         transition-all duration-300 hover:border-gray-300 resize-none shadow-sm;
}

.form-textarea:focus {
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1), 0 1px 3px 0 rgb(0 0 0 / 0.1);
}

.btn-primary {
  @apply bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800
         text-white px-6 py-3.5 rounded-xl font-semibold transition-all duration-300
         transform hover:scale-[1.02] hover:shadow-lg active:scale-[0.98]
         focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2;
}

.btn-secondary {
  @apply bg-white border border-gray-200 hover:bg-gray-50 hover:border-gray-300
         text-gray-700 px-6 py-3.5 rounded-xl font-semibold transition-all duration-300
         transform hover:scale-[1.02] hover:shadow-md active:scale-[0.98]
         focus:outline-none focus:ring-2 focus:ring-gray-500/50 focus:ring-offset-2;
}

.btn-danger {
  @apply bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800
         text-white px-6 py-3.5 rounded-xl font-semibold transition-all duration-300
         transform hover:scale-[1.02] hover:shadow-lg active:scale-[0.98]
         focus:outline-none focus:ring-2 focus:ring-red-500/50 focus:ring-offset-2;
}

.btn-success {
  @apply bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800
         text-white px-6 py-3.5 rounded-xl font-semibold transition-all duration-300
         transform hover:scale-[1.02] hover:shadow-lg active:scale-[0.98]
         focus:outline-none focus:ring-2 focus:ring-green-500/50 focus:ring-offset-2;
}

/* Card Styles */
.card {
  @apply bg-white rounded-2xl shadow-sm border border-gray-100 p-8
         hover:shadow-md transition-all duration-300 hover:border-gray-200;
}

.card-header {
  @apply border-b border-gray-100 pb-6 mb-8;
}

.card-elevated {
  @apply bg-white rounded-2xl shadow-lg border border-gray-100 p-8
         hover:shadow-xl transition-all duration-300;
}

.glass-card {
  @apply bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20
         shadow-lg hover:shadow-xl transition-all duration-300;
}

/* Table Styles */
.table {
  @apply w-full border-collapse bg-white rounded-lg overflow-hidden shadow-sm;
}

.table th {
  @apply bg-gray-50 px-6 py-4 text-right text-sm font-medium text-gray-900 border-b border-gray-200;
}

.table td {
  @apply px-6 py-4 text-sm text-gray-900 border-b border-gray-200;
}

.table tr:hover {
  @apply bg-gray-50;
}

/* Modal Styles */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-content {
  @apply bg-white rounded-xl shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto;
}

/* Sidebar Styles */
.sidebar-link {
  @apply flex items-center space-x-3 rtl:space-x-reverse px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-all duration-200;
}

.sidebar-link.active {
  @apply bg-blue-100 text-blue-600 border-l-4 border-blue-600;
}

/* Loading Spinner */
.spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600;
}

/* Image Upload Area */
.image-upload-area {
  @apply border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors duration-200 cursor-pointer;
}

.image-upload-area.dragover {
  @apply border-blue-500 bg-blue-50;
}

/* Status Badges */
.badge {
  @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
}

.badge-success {
  @apply bg-green-100 text-green-800;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.badge-error {
  @apply bg-red-100 text-red-800;
}

.badge-info {
  @apply bg-blue-100 text-blue-800;
}
