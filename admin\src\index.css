@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: 'Inter', 'Cairo', sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* Modern Color Palette */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;

  --secondary-50: #fdf4ff;
  --secondary-100: #fae8ff;
  --secondary-200: #f5d0fe;
  --secondary-300: #f0abfc;
  --secondary-400: #e879f9;
  --secondary-500: #d946ef;
  --secondary-600: #c026d3;
  --secondary-700: #a21caf;
  --secondary-800: #86198f;
  --secondary-900: #701a75;

  --accent-50: #fff7ed;
  --accent-100: #ffedd5;
  --accent-200: #fed7aa;
  --accent-300: #fdba74;
  --accent-400: #fb923c;
  --accent-500: #f97316;
  --accent-600: #ea580c;
  --accent-700: #c2410c;
  --accent-800: #9a3412;
  --accent-900: #7c2d12;

  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Success Colors */
  --success-50: #f0fdf4;
  --success-100: #dcfce7;
  --success-500: #22c55e;
  --success-600: #16a34a;
  --success-700: #15803d;

  /* Warning Colors */
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;

  /* Error Colors */
  --error-50: #fef2f2;
  --error-100: #fee2e2;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --error-700: #b91c1c;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-500), var(--primary-700));
  --gradient-secondary: linear-gradient(135deg, var(--secondary-500), var(--secondary-700));
  --gradient-accent: linear-gradient(135deg, var(--accent-500), var(--accent-700));
  --gradient-rainbow: linear-gradient(135deg, var(--primary-500), var(--secondary-500), var(--accent-500));

  color-scheme: light;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  font-family: 'Inter', 'Cairo', sans-serif;
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--primary-50) 100%);
  color: var(--gray-900);
  overflow-x: hidden;
}

#root {
  width: 100%;
  min-height: 100vh;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  border-radius: 4px;
  border: 2px solid var(--gray-100);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
}

::-webkit-scrollbar-corner {
  background: var(--gray-100);
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(32px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-left {
  animation: slideInLeft 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.scale-in {
  animation: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.bounce-in {
  animation: bounceIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes bounceIn {
  from {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite alternate;
}

@keyframes pulseGlow {
  from {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  to {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
  }
}

.gradient-shift {
  background-size: 200% 200%;
  animation: gradientShift 4s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Form Styles */
.form-input {
  @apply w-full px-5 py-4 border border-gray-200 rounded-2xl bg-white/80 backdrop-blur-sm text-gray-900 placeholder-gray-400
         focus:ring-2 focus:ring-primary-500/30 focus:border-primary-500 focus:outline-none
         transition-all duration-300 hover:border-gray-300 hover:bg-white shadow-sm hover:shadow-md;
}

.form-input:focus {
  box-shadow: 0 0 0 4px rgb(14 165 233 / 0.15), 0 4px 12px 0 rgb(14 165 233 / 0.1);
  background: white;
}

.form-textarea {
  @apply w-full px-5 py-4 border border-gray-200 rounded-2xl bg-white/80 backdrop-blur-sm text-gray-900 placeholder-gray-400
         focus:ring-2 focus:ring-primary-500/30 focus:border-primary-500 focus:outline-none
         transition-all duration-300 hover:border-gray-300 hover:bg-white resize-none shadow-sm hover:shadow-md;
}

.form-textarea:focus {
  box-shadow: 0 0 0 4px rgb(14 165 233 / 0.15), 0 4px 12px 0 rgb(14 165 233 / 0.1);
  background: white;
}

.btn-primary {
  @apply bg-gradient-to-r from-primary-500 via-primary-600 to-primary-700
         hover:from-primary-600 hover:via-primary-700 hover:to-primary-800
         text-white px-8 py-4 rounded-2xl font-bold transition-all duration-300
         transform hover:scale-[1.02] hover:shadow-xl active:scale-[0.98]
         focus:outline-none focus:ring-4 focus:ring-primary-500/30 focus:ring-offset-2
         shadow-lg shadow-primary-500/25 relative overflow-hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-secondary {
  @apply bg-white/90 backdrop-blur-sm border border-gray-200 hover:bg-white hover:border-gray-300
         text-gray-700 px-8 py-4 rounded-2xl font-bold transition-all duration-300
         transform hover:scale-[1.02] hover:shadow-lg active:scale-[0.98]
         focus:outline-none focus:ring-4 focus:ring-gray-500/30 focus:ring-offset-2
         shadow-md hover:shadow-xl;
}

.btn-danger {
  @apply bg-gradient-to-r from-error-500 via-error-600 to-error-700
         hover:from-error-600 hover:via-error-700 hover:to-error-800
         text-white px-8 py-4 rounded-2xl font-bold transition-all duration-300
         transform hover:scale-[1.02] hover:shadow-xl active:scale-[0.98]
         focus:outline-none focus:ring-4 focus:ring-error-500/30 focus:ring-offset-2
         shadow-lg shadow-error-500/25;
}

.btn-success {
  @apply bg-gradient-to-r from-success-500 via-success-600 to-success-700
         hover:from-success-600 hover:via-success-700 hover:to-success-800
         text-white px-8 py-4 rounded-2xl font-bold transition-all duration-300
         transform hover:scale-[1.02] hover:shadow-xl active:scale-[0.98]
         focus:outline-none focus:ring-4 focus:ring-success-500/30 focus:ring-offset-2
         shadow-lg shadow-success-500/25;
}

.btn-accent {
  @apply bg-gradient-to-r from-accent-500 via-accent-600 to-accent-700
         hover:from-accent-600 hover:via-accent-700 hover:to-accent-800
         text-white px-8 py-4 rounded-2xl font-bold transition-all duration-300
         transform hover:scale-[1.02] hover:shadow-xl active:scale-[0.98]
         focus:outline-none focus:ring-4 focus:ring-accent-500/30 focus:ring-offset-2
         shadow-lg shadow-accent-500/25;
}

/* Card Styles */
.card {
  @apply bg-white/90 backdrop-blur-sm rounded-3xl shadow-lg border border-gray-100/50 p-8
         hover:shadow-xl hover:bg-white transition-all duration-500 hover:border-gray-200/50
         transform hover:scale-[1.01] relative overflow-hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card:hover::before {
  opacity: 1;
}

.card-header {
  @apply border-b border-gray-100/50 pb-8 mb-8 relative;
}

.card-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: var(--gradient-primary);
  border-radius: 2px;
}

.card-elevated {
  @apply bg-white/95 backdrop-blur-md rounded-3xl shadow-2xl border border-gray-100/50 p-10
         hover:shadow-3xl transition-all duration-500 relative overflow-hidden;
}

.card-elevated::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: var(--gradient-rainbow);
  opacity: 0.8;
}

.glass-card {
  @apply bg-white/70 backdrop-blur-xl rounded-3xl border border-white/30
         shadow-2xl hover:shadow-3xl transition-all duration-500
         hover:bg-white/80 relative overflow-hidden;
}

.glass-card::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: var(--gradient-primary);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  opacity: 0.3;
}

/* Table Styles */
.table {
  @apply w-full border-collapse bg-white rounded-lg overflow-hidden shadow-sm;
}

.table th {
  @apply bg-gray-50 px-6 py-4 text-right text-sm font-medium text-gray-900 border-b border-gray-200;
}

.table td {
  @apply px-6 py-4 text-sm text-gray-900 border-b border-gray-200;
}

.table tr:hover {
  @apply bg-gray-50;
}

/* Modal Styles */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-content {
  @apply bg-white rounded-xl shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto;
}

/* Sidebar Styles */
.sidebar-link {
  @apply flex items-center space-x-3 rtl:space-x-reverse px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-all duration-200;
}

.sidebar-link.active {
  @apply bg-blue-100 text-blue-600 border-l-4 border-blue-600;
}

/* Loading Spinner */
.spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600;
}

/* Image Upload Area */
.image-upload-area {
  @apply border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors duration-200 cursor-pointer;
}

.image-upload-area.dragover {
  @apply border-blue-500 bg-blue-50;
}

/* Status Badges */
.badge {
  @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
}

.badge-success {
  @apply bg-green-100 text-green-800;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.badge-error {
  @apply bg-red-100 text-red-800;
}

.badge-info {
  @apply bg-blue-100 text-blue-800;
}

/* Modern Enhancements */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

.backdrop-blur-2xl {
  backdrop-filter: blur(40px);
}

.backdrop-blur-3xl {
  backdrop-filter: blur(64px);
}

.border-3 {
  border-width: 3px;
}

/* Glassmorphism Effects */
.glass-morphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-morphism-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
}

/* Neumorphism Effects */
.neumorphism {
  background: #f0f0f0;
  box-shadow: 20px 20px 60px #bebebe, -20px -20px 60px #ffffff;
}

.neumorphism-inset {
  background: #f0f0f0;
  box-shadow: inset 20px 20px 60px #bebebe, inset -20px -20px 60px #ffffff;
}

/* Advanced Hover Effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.hover-glow {
  transition: all 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
}

/* Gradient Text Effects */
.gradient-text-primary {
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-rainbow {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

/* Modern Scrollbar */
.modern-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.modern-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.modern-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  border-radius: 10px;
}

.modern-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
}

/* Loading Animations */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: loadingDots 1.5s infinite;
}

@keyframes loadingDots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

/* Ripple Effect */
.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
  width: 300px;
  height: 300px;
}
