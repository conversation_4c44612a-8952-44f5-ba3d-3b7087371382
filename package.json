{"name": "expert-wonders-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "admin": "vite --config admin/vite.config.js"}, "dependencies": {"framer-motion": "^12.20.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "swiper": "^11.2.10"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.3", "vite": "^7.0.0"}}