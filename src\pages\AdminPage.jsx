import Login from '../admin/components/Login'
import Dashboard from '../admin/components/Dashboard'
import { AuthProvider, useAuth } from '../admin/context/AuthContext'
import { DataProvider } from '../admin/context/DataContext'

function AdminContent() {
  const { isAuthenticated, loading } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-6"></div>
          <p className="text-gray-600 text-xl font-bold">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30" dir="rtl">
      {isAuthenticated ? (
        <DataProvider>
          <Dashboard />
        </DataProvider>
      ) : (
        <Login />
      )}
    </div>
  )
}

function AdminPage() {
  return (
    <AuthProvider>
      <AdminContent />
    </AuthProvider>
  )
}

export default AdminPage
