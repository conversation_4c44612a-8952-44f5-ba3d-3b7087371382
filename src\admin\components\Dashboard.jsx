import { useState } from 'react'
import Sidebar from './Sidebar'
import Header from './Header'
import HeroManagement from './sections/HeroManagement'
import WhyChooseUsManagement from './sections/WhyChooseUsManagement'
import KitchensManagement from './sections/KitchensManagement'
import CabinetsManagement from './sections/CabinetsManagement'
import FooterManagement from './sections/FooterManagement'
import UserManagement from './sections/UserManagement'
import DashboardHome from './sections/DashboardHome'

const Dashboard = () => {
  const [activeSection, setActiveSection] = useState('dashboard')
  const [sidebarOpen, setSidebarOpen] = useState(false)

  const renderActiveSection = () => {
    switch (activeSection) {
      case 'dashboard':
        return <DashboardHome />
      case 'hero':
        return <HeroManagement />
      case 'why-choose-us':
        return <WhyChooseUsManagement />
      case 'kitchens':
        return <KitchensManagement />
      case 'cabinets':
        return <CabinetsManagement />
      case 'footer':
        return <FooterManagement />
      case 'users':
        return <UserManagement />
      default:
        return <DashboardHome />
    }
  }

  return (
    <div className="flex h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -left-40 w-80 h-80 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -right-40 w-96 h-96 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-indigo-400/5 to-blue-400/5 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Sidebar */}
      <Sidebar
        activeSection={activeSection}
        setActiveSection={setActiveSection}
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden relative z-10">
        {/* Header */}
        <Header
          setSidebarOpen={setSidebarOpen}
          activeSection={activeSection}
        />

        {/* Content */}
        <main className="flex-1 overflow-x-hidden overflow-y-auto p-8 relative">
          <div className="max-w-7xl mx-auto">
            <div className="fade-in">
              {renderActiveSection()}
            </div>
          </div>
        </main>
      </div>

      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/60 backdrop-blur-sm lg:hidden transition-all duration-300"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  )
}

export default Dashboard
