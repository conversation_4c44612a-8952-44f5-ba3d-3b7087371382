import { useState } from 'react'
import Sidebar from './Sidebar'
import Header from './Header'
import HeroManagement from './sections/HeroManagement'
import WhyChooseUsManagement from './sections/WhyChooseUsManagement'
import KitchensManagement from './sections/KitchensManagement'
import CabinetsManagement from './sections/CabinetsManagement'
import FooterManagement from './sections/FooterManagement'
import UserManagement from './sections/UserManagement'
import DashboardHome from './sections/DashboardHome'

const Dashboard = () => {
  const [activeSection, setActiveSection] = useState('dashboard')
  const [sidebarOpen, setSidebarOpen] = useState(false)

  const renderActiveSection = () => {
    switch (activeSection) {
      case 'dashboard':
        return <DashboardHome />
      case 'hero':
        return <HeroManagement />
      case 'why-choose-us':
        return <WhyChooseUsManagement />
      case 'kitchens':
        return <KitchensManagement />
      case 'cabinets':
        return <CabinetsManagement />
      case 'footer':
        return <FooterManagement />
      case 'users':
        return <UserManagement />
      default:
        return <DashboardHome />
    }
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <Sidebar 
        activeSection={activeSection}
        setActiveSection={setActiveSection}
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header 
          setSidebarOpen={setSidebarOpen}
          activeSection={activeSection}
        />

        {/* Content */}
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6">
          <div className="max-w-7xl mx-auto">
            {renderActiveSection()}
          </div>
        </main>
      </div>

      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  )
}

export default Dashboard
