import { useAuth } from '../context/AuthContext'

const Sidebar = ({ activeSection, setActiveSection, sidebarOpen, setSidebarOpen }) => {
  const { logout, user } = useAuth()

  const menuItems = [
    {
      id: 'dashboard',
      name: 'الرئيسية',
      icon: 'ri-dashboard-line',
      description: 'نظرة عامة على النظام'
    },
    {
      id: 'hero',
      name: 'إدارة الهيرو',
      icon: 'ri-image-line',
      description: 'تحرير صور ونصوص القسم الرئيسي'
    },
    {
      id: 'why-choose-us',
      name: 'لماذا تختارنا',
      icon: 'ri-star-line',
      description: 'إدارة مميزات الشركة'
    },
    {
      id: 'kitchens',
      name: 'إدارة المطابخ',
      icon: 'ri-home-4-line',
      description: 'إضافة وتعديل معرض المطابخ'
    },
    {
      id: 'cabinets',
      name: 'إدارة الخزائن',
      icon: 'ri-archive-line',
      description: 'إضافة وتعديل معرض الخزائن'
    },
    {
      id: 'footer',
      name: 'إدارة الفوتر',
      icon: 'ri-links-line',
      description: 'تحرير روابط التواصل والمعلومات'
    },
    {
      id: 'users',
      name: 'إدارة المستخدمين',
      icon: 'ri-user-settings-line',
      description: 'تعديل بيانات المستخدم'
    }
  ]

  const handleLogout = () => {
    if (window.confirm('هل أنت متأكد من تسجيل الخروج؟')) {
      logout()
    }
  }

  return (
    <>
      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 right-0 z-50 w-80 bg-gradient-to-b from-white via-gray-50/50 to-white backdrop-blur-xl
        border-l border-gray-200/50 shadow-2xl transform transition-all duration-500 ease-out
        lg:translate-x-0 lg:static lg:inset-0
        ${sidebarOpen ? 'translate-x-0' : 'translate-x-full'}
      `}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-8 border-b border-gray-100/50">
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <div className="relative">
                <div className="w-14 h-14 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <i className="ri-admin-line text-white text-2xl"></i>
                </div>
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur opacity-20 animate-pulse"></div>
              </div>
              <div>
                <h2 className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">لوحة التحكم</h2>
                <p className="text-sm text-gray-600 font-medium">عجائب الخبراء</p>
              </div>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden p-3 rounded-xl hover:bg-gray-100 transition-all duration-300 hover:scale-110"
            >
              <i className="ri-close-line text-xl text-gray-500"></i>
            </button>
          </div>

          {/* User Info */}
          <div className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-200">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-lg">
                  {user?.username?.charAt(0)?.toUpperCase() || 'A'}
                </span>
              </div>
              <div>
                <h3 className="font-medium text-gray-900">{user?.username || 'المدير'}</h3>
                <p className="text-sm text-gray-600">{user?.email || '<EMAIL>'}</p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
            {menuItems.map((item) => (
              <button
                key={item.id}
                onClick={() => {
                  setActiveSection(item.id)
                  setSidebarOpen(false)
                }}
                className={`
                  w-full text-right p-4 rounded-xl transition-all duration-200 group
                  ${activeSection === item.id
                    ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg'
                    : 'hover:bg-gray-50 text-gray-700 hover:text-blue-600'
                  }
                `}
              >
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                  <div className={`
                    w-10 h-10 rounded-lg flex items-center justify-center transition-colors
                    ${activeSection === item.id
                      ? 'bg-white/20'
                      : 'bg-gray-100 group-hover:bg-blue-100'
                    }
                  `}>
                    <i className={`${item.icon} text-xl ${
                      activeSection === item.id ? 'text-white' : 'text-gray-600 group-hover:text-blue-600'
                    }`}></i>
                  </div>
                  <div className="flex-1 text-right">
                    <h3 className="font-medium">{item.name}</h3>
                    <p className={`text-sm ${
                      activeSection === item.id ? 'text-white/80' : 'text-gray-500'
                    }`}>
                      {item.description}
                    </p>
                  </div>
                </div>
              </button>
            ))}
          </nav>

          {/* Logout Button */}
          <div className="p-4 border-t border-gray-200">
            <button
              onClick={handleLogout}
              className="w-full flex items-center justify-center space-x-3 rtl:space-x-reverse p-4 text-red-600 hover:bg-red-50 rounded-xl transition-all duration-200 group"
            >
              <i className="ri-logout-box-line text-xl"></i>
              <span className="font-medium">تسجيل الخروج</span>
            </button>
          </div>
        </div>
      </div>
    </>
  )
}

export default Sidebar
